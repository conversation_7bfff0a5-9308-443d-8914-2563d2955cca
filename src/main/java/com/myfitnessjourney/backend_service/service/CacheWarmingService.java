package com.myfitnessjourney.backend_service.service;

import com.myfitnessjourney.backend_service.dto.FoodSearchResponseDto;
import com.myfitnessjourney.backend_service.repository.FoodNutrientsRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class CacheWarmingService {

    private final FoodSearchService foodSearchService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final FoodNutrientsRepository foodNutrientsRepository;

    // Popular food search terms to pre-cache
    private static final List<String> POPULAR_SEARCHES = Arrays.asList(
        "apple", "banana", "chicken", "rice", "bread", "milk", "egg", "cheese",
        "tomato", "potato", "beef", "salmon", "yogurt", "pasta", "oats",
        "avocado", "spinach", "broccoli", "orange", "strawberry", "almonds",
        "quinoa", "sweet potato", "tuna", "olive oil", "honey", "garlic",
        "onion", "carrot", "cucumber", "lettuce", "turkey", "pork", "shrimp",
        "beans", "lentils", "chickpeas", "tofu", "nuts", "seeds", "berries"
    );

    @EventListener(ApplicationReadyEvent.class)
    @Async
    public void warmCacheOnStartup() {
        log.info("Starting cache warming process...");
        
        try {
            // Warm cache with popular searches
            warmPopularSearches();
            
            // Warm cache with random samples for better coverage
            warmRandomSamples();
            
            log.info("Cache warming completed successfully");
        } catch (Exception e) {
            log.error("Error during cache warming", e);
        }
    }

    @Scheduled(fixedRate = 3600000) // Run every hour
    @Async
    public void scheduledCacheWarming() {
        log.debug("Running scheduled cache warming...");
        
        try {
            // Refresh popular searches
            warmPopularSearches();
            
            // Clean up expired cache entries
            cleanupExpiredEntries();
            
        } catch (Exception e) {
            log.error("Error during scheduled cache warming", e);
        }
    }

    private void warmPopularSearches() {
        log.info("Warming cache with {} popular searches", POPULAR_SEARCHES.size());
        
        for (String searchTerm : POPULAR_SEARCHES) {
            try {
                // This will populate the cache if not already present
                List<FoodSearchResponseDto> results = foodSearchService.searchFoods(searchTerm);
                log.debug("Warmed cache for '{}' with {} results", searchTerm, results.size());
                
                // Small delay to avoid overwhelming the database
                Thread.sleep(50);
                
            } catch (Exception e) {
                log.warn("Failed to warm cache for search term: {}", searchTerm, e);
            }
        }
    }

    private void warmRandomSamples() {
        log.info("Warming cache with random food samples...");
        
        try {
            // Get a sample of product names for cache warming
            List<String> sampleProductNames = getSampleProductNames();
            
            for (String productName : sampleProductNames) {
                try {
                    // Extract first word for search
                    String searchTerm = extractSearchTerm(productName);
                    if (searchTerm.length() >= 3) {
                        foodSearchService.searchFoods(searchTerm);
                        Thread.sleep(100); // Longer delay for random samples
                    }
                } catch (Exception e) {
                    log.debug("Failed to warm cache for product: {}", productName);
                }
            }
            
        } catch (Exception e) {
            log.warn("Error warming cache with random samples", e);
        }
    }

    private List<String> getSampleProductNames() {
        // Get a sample of product names from the database
        // This is a simplified approach - in production, you might want to use a more sophisticated sampling
        try {
            return foodNutrientsRepository.findAll()
                .stream()
                .limit(100)
                .map(food -> food.getProductName())
                .filter(name -> name != null && name.length() > 3)
                .toList();
        } catch (Exception e) {
            log.warn("Failed to get sample product names", e);
            return List.of();
        }
    }

    private String extractSearchTerm(String productName) {
        if (productName == null || productName.trim().isEmpty()) {
            return "";
        }
        
        // Extract the first meaningful word (skip common prefixes)
        String[] words = productName.toLowerCase().split("\\s+");
        for (String word : words) {
            if (word.length() >= 3 && !isCommonPrefix(word)) {
                return word;
            }
        }
        
        return words.length > 0 ? words[0] : "";
    }

    private boolean isCommonPrefix(String word) {
        // Skip common prefixes that don't add search value
        return word.equals("the") || word.equals("and") || word.equals("with") || 
               word.equals("for") || word.equals("organic") || word.equals("fresh");
    }

    private void cleanupExpiredEntries() {
        log.debug("Cleaning up expired cache entries...");
        
        try {
            // Get all cache keys with our prefixes
            String[] patterns = {"food_search:*", "search_count:*", "popular_search:*", "exact_match:*"};
            
            for (String pattern : patterns) {
                var keys = redisTemplate.keys(pattern);
                if (keys != null && !keys.isEmpty()) {
                    // Check TTL and remove keys that are close to expiring
                    for (String key : keys) {
                        Long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);
                        if (ttl != null && ttl < 300) { // Less than 5 minutes remaining
                            redisTemplate.delete(key);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.warn("Error during cache cleanup", e);
        }
    }

    // Manual cache warming endpoint (can be called via admin interface)
    public void manualCacheWarm(List<String> searchTerms) {
        log.info("Manual cache warming requested for {} terms", searchTerms.size());
        
        for (String term : searchTerms) {
            try {
                foodSearchService.searchFoods(term);
                log.debug("Manually warmed cache for: {}", term);
            } catch (Exception e) {
                log.warn("Failed to manually warm cache for: {}", term, e);
            }
        }
    }

    // Get cache statistics
    public CacheStats getCacheStats() {
        try {
            var searchKeys = redisTemplate.keys("food_search:*");
            var popularKeys = redisTemplate.keys("popular_search:*");
            var exactKeys = redisTemplate.keys("exact_match:*");
            var countKeys = redisTemplate.keys("search_count:*");
            
            return new CacheStats(
                searchKeys != null ? searchKeys.size() : 0,
                popularKeys != null ? popularKeys.size() : 0,
                exactKeys != null ? exactKeys.size() : 0,
                countKeys != null ? countKeys.size() : 0
            );
        } catch (Exception e) {
            log.error("Error getting cache stats", e);
            return new CacheStats(0, 0, 0, 0);
        }
    }

    public record CacheStats(
        int searchCacheSize,
        int popularCacheSize,
        int exactMatchCacheSize,
        int searchCountSize
    ) {}
}
