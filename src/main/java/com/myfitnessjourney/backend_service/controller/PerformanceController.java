package com.myfitnessjourney.backend_service.controller;

import com.myfitnessjourney.backend_service.service.CacheWarmingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/admin/performance")
@RequiredArgsConstructor
@Slf4j
public class PerformanceController {

    private final CacheWarmingService cacheWarmingService;
    private final RedisTemplate<String, Object> redisTemplate;

    @GetMapping("/cache/stats")
    public ResponseEntity<CacheWarmingService.CacheStats> getCacheStats() {
        log.info("Cache stats requested");
        return ResponseEntity.ok(cacheWarmingService.getCacheStats());
    }

    @PostMapping("/cache/warm")
    public ResponseEntity<Map<String, String>> warmCache(@RequestBody List<String> searchTerms) {
        log.info("Manual cache warming requested for {} terms", searchTerms.size());
        
        try {
            cacheWarmingService.manualCacheWarm(searchTerms);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Cache warming initiated for " + searchTerms.size() + " terms");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error during manual cache warming", e);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Cache warming failed: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @DeleteMapping("/cache/clear")
    public ResponseEntity<Map<String, String>> clearCache() {
        log.info("Cache clear requested");
        
        try {
            // Clear all food search related caches
            String[] patterns = {"food_search:*", "search_count:*", "popular_search:*", "exact_match:*"};
            int totalCleared = 0;
            
            for (String pattern : patterns) {
                var keys = redisTemplate.keys(pattern);
                if (keys != null && !keys.isEmpty()) {
                    redisTemplate.delete(keys);
                    totalCleared += keys.size();
                }
            }
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Cleared " + totalCleared + " cache entries");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error clearing cache", e);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Cache clear failed: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/redis/info")
    public ResponseEntity<Map<String, Object>> getRedisInfo() {
        log.info("Redis info requested");
        
        try {
            Map<String, Object> info = new HashMap<>();
            
            // Get basic Redis info
            var connection = redisTemplate.getConnectionFactory().getConnection();
            var redisInfo = connection.info();
            
            // Parse relevant metrics
            String[] lines = redisInfo.split("\n");
            for (String line : lines) {
                if (line.startsWith("used_memory_human:") || 
                    line.startsWith("connected_clients:") ||
                    line.startsWith("total_commands_processed:") ||
                    line.startsWith("keyspace_hits:") ||
                    line.startsWith("keyspace_misses:")) {
                    
                    String[] parts = line.split(":");
                    if (parts.length == 2) {
                        info.put(parts[0].trim(), parts[1].trim());
                    }
                }
            }
            
            // Calculate hit ratio
            String hits = (String) info.get("keyspace_hits");
            String misses = (String) info.get("keyspace_misses");
            if (hits != null && misses != null) {
                try {
                    long hitCount = Long.parseLong(hits);
                    long missCount = Long.parseLong(misses);
                    double hitRatio = hitCount + missCount > 0 ? 
                        (double) hitCount / (hitCount + missCount) * 100 : 0;
                    info.put("hit_ratio_percent", String.format("%.2f", hitRatio));
                } catch (NumberFormatException e) {
                    log.warn("Could not calculate hit ratio", e);
                }
            }
            
            connection.close();
            
            return ResponseEntity.ok(info);
        } catch (Exception e) {
            log.error("Error getting Redis info", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to get Redis info: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/database/stats")
    public ResponseEntity<Map<String, Object>> getDatabaseStats() {
        log.info("Database stats requested");
        
        // This would typically query database statistics
        // For now, return a placeholder response
        Map<String, Object> stats = new HashMap<>();
        stats.put("status", "Database stats endpoint - implement based on your monitoring needs");
        stats.put("note", "Consider adding queries for table sizes, index usage, query performance, etc.");
        
        return ResponseEntity.ok(stats);
    }
}
