databaseChangeLog:
  - changeSet:
      id: 011-create-performance-indexes
      author: f<PERSON><PERSON><PERSON><PERSON>
      comment: "Create additional performance indexes for food search optimization"
      changes:
        - sql:
            sql: |
              -- Composite index for combined search vector and similarity queries
              CREATE INDEX CONCURRENTLY idx_food_search_combined 
              ON food_nutrients (search_vector, product_name) 
              WHERE search_vector IS NOT NULL;

        - sql:
            sql: |
              -- Partial index for high-quality food items (with complete nutrition data)
              CREATE INDEX CONCURRENTLY idx_food_complete_nutrition 
              ON food_nutrients (product_name, brand_tag) 
              WHERE energy_kcal_100g IS NOT NULL 
                AND proteins_100g IS NOT NULL 
                AND carbohydrates_100g IS NOT NULL 
                AND fat_100g IS NOT NULL;

        - sql:
            sql: |
              -- Index for popular categories (covering index)
              CREATE INDEX CONCURRENTLY idx_food_popular_categories 
              ON food_nutrients (category_tags, product_name, brand_tag, energy_kcal_100g) 
              WHERE category_tags IS NOT NULL AND array_length(category_tags, 1) > 0;

        - sql:
            sql: |
              -- Prefix index for fast autocomplete on product names
              CREATE INDEX CONCURRENTLY idx_food_name_prefix 
              ON food_nutrients (left(product_name, 10), product_name);

        - sql:
            sql: |
              -- Index for exact code lookups (most common search pattern)
              CREATE UNIQUE INDEX CONCURRENTLY idx_food_code_unique 
              ON food_nutrients (code) 
              WHERE code IS NOT NULL;

        - sql:
            sql: |
              -- Covering index for search results (includes all commonly accessed columns)
              CREATE INDEX CONCURRENTLY idx_food_search_covering 
              ON food_nutrients (search_vector, product_name, code, brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g) 
              WHERE search_vector IS NOT NULL;

        - sql:
            sql: |
              -- Update table statistics for better query planning
              ANALYZE food_nutrients;

        - sql:
            sql: |
              -- Set PostgreSQL configuration for better full-text search performance
              -- These would typically be set at the database level, but documenting here
              -- ALTER SYSTEM SET default_text_search_config = 'pg_catalog.english';
              -- ALTER SYSTEM SET work_mem = '256MB';
              -- ALTER SYSTEM SET maintenance_work_mem = '1GB';
              -- ALTER SYSTEM SET effective_cache_size = '4GB';
              -- SELECT pg_reload_conf();
              
              -- For now, just ensure our search configuration is optimal
              SELECT set_config('default_text_search_config', 'pg_catalog.english', false);

      rollback:
        - sql:
            sql: |
              DROP INDEX CONCURRENTLY IF EXISTS idx_food_search_combined;
              DROP INDEX CONCURRENTLY IF EXISTS idx_food_complete_nutrition;
              DROP INDEX CONCURRENTLY IF EXISTS idx_food_popular_categories;
              DROP INDEX CONCURRENTLY IF EXISTS idx_food_name_prefix;
              DROP INDEX CONCURRENTLY IF EXISTS idx_food_code_unique;
              DROP INDEX CONCURRENTLY IF EXISTS idx_food_search_covering;
