databaseChangeLog:
  - changeSet:
      id: 010-create-food-partitioning
      author: <PERSON><PERSON><PERSON><PERSON><PERSON>
      comment: "Create partitioned food_nutrients table for better performance"
      changes:
        - sql:
            sql: |
              -- Create new partitioned table
              CREATE TABLE food_nutrients_partitioned (
                  id SERIAL,
                  code TEXT NOT NULL,
                  product_name TEXT NOT NULL,
                  brand_tag TEXT,
                  category_tags TEXT[],
                  country_tags TEXT[],
                  image_url TEXT,
                  energy_kcal_100g NUMERIC,
                  fat_100g NUMERIC,
                  proteins_100g NUMERIC,
                  carbohydrates_100g NUMERIC,
                  sugars_100g NUMERIC,
                  salt_100g NUMERIC,
                  sodium_100g NUMERIC,
                  search_vector TSVECTOR,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (id, code)
              ) PARTITION BY HASH (code);

        - sql:
            sql: |
              -- Create 8 partitions for hash distribution
              CREATE TABLE food_nutrients_part_0 PARTITION OF food_nutrients_partitioned
                  FOR VALUES WITH (modulus 8, remainder 0);
              
              CREATE TABLE food_nutrients_part_1 PARTITION OF food_nutrients_partitioned
                  FOR VALUES WITH (modulus 8, remainder 1);
              
              CREATE TABLE food_nutrients_part_2 PARTITION OF food_nutrients_partitioned
                  FOR VALUES WITH (modulus 8, remainder 2);
              
              CREATE TABLE food_nutrients_part_3 PARTITION OF food_nutrients_partitioned
                  FOR VALUES WITH (modulus 8, remainder 3);
              
              CREATE TABLE food_nutrients_part_4 PARTITION OF food_nutrients_partitioned
                  FOR VALUES WITH (modulus 8, remainder 4);
              
              CREATE TABLE food_nutrients_part_5 PARTITION OF food_nutrients_partitioned
                  FOR VALUES WITH (modulus 8, remainder 5);
              
              CREATE TABLE food_nutrients_part_6 PARTITION OF food_nutrients_partitioned
                  FOR VALUES WITH (modulus 8, remainder 6);
              
              CREATE TABLE food_nutrients_part_7 PARTITION OF food_nutrients_partitioned
                  FOR VALUES WITH (modulus 8, remainder 7);

        - sql:
            sql: |
              -- Create indexes on each partition
              CREATE INDEX idx_part_0_search_vector ON food_nutrients_part_0 USING GIN (search_vector);
              CREATE INDEX idx_part_0_product_name_trgm ON food_nutrients_part_0 USING GIN (product_name gin_trgm_ops);
              CREATE INDEX idx_part_0_category_tags ON food_nutrients_part_0 USING GIN (category_tags);
              CREATE INDEX idx_part_0_brand_tag ON food_nutrients_part_0 (brand_tag);
              CREATE INDEX idx_part_0_code ON food_nutrients_part_0 (code);

              CREATE INDEX idx_part_1_search_vector ON food_nutrients_part_1 USING GIN (search_vector);
              CREATE INDEX idx_part_1_product_name_trgm ON food_nutrients_part_1 USING GIN (product_name gin_trgm_ops);
              CREATE INDEX idx_part_1_category_tags ON food_nutrients_part_1 USING GIN (category_tags);
              CREATE INDEX idx_part_1_brand_tag ON food_nutrients_part_1 (brand_tag);
              CREATE INDEX idx_part_1_code ON food_nutrients_part_1 (code);

              CREATE INDEX idx_part_2_search_vector ON food_nutrients_part_2 USING GIN (search_vector);
              CREATE INDEX idx_part_2_product_name_trgm ON food_nutrients_part_2 USING GIN (product_name gin_trgm_ops);
              CREATE INDEX idx_part_2_category_tags ON food_nutrients_part_2 USING GIN (category_tags);
              CREATE INDEX idx_part_2_brand_tag ON food_nutrients_part_2 (brand_tag);
              CREATE INDEX idx_part_2_code ON food_nutrients_part_2 (code);

              CREATE INDEX idx_part_3_search_vector ON food_nutrients_part_3 USING GIN (search_vector);
              CREATE INDEX idx_part_3_product_name_trgm ON food_nutrients_part_3 USING GIN (product_name gin_trgm_ops);
              CREATE INDEX idx_part_3_category_tags ON food_nutrients_part_3 USING GIN (category_tags);
              CREATE INDEX idx_part_3_brand_tag ON food_nutrients_part_3 (brand_tag);
              CREATE INDEX idx_part_3_code ON food_nutrients_part_3 (code);

              CREATE INDEX idx_part_4_search_vector ON food_nutrients_part_4 USING GIN (search_vector);
              CREATE INDEX idx_part_4_product_name_trgm ON food_nutrients_part_4 USING GIN (product_name gin_trgm_ops);
              CREATE INDEX idx_part_4_category_tags ON food_nutrients_part_4 USING GIN (category_tags);
              CREATE INDEX idx_part_4_brand_tag ON food_nutrients_part_4 (brand_tag);
              CREATE INDEX idx_part_4_code ON food_nutrients_part_4 (code);

              CREATE INDEX idx_part_5_search_vector ON food_nutrients_part_5 USING GIN (search_vector);
              CREATE INDEX idx_part_5_product_name_trgm ON food_nutrients_part_5 USING GIN (product_name gin_trgm_ops);
              CREATE INDEX idx_part_5_category_tags ON food_nutrients_part_5 USING GIN (category_tags);
              CREATE INDEX idx_part_5_brand_tag ON food_nutrients_part_5 (brand_tag);
              CREATE INDEX idx_part_5_code ON food_nutrients_part_5 (code);

              CREATE INDEX idx_part_6_search_vector ON food_nutrients_part_6 USING GIN (search_vector);
              CREATE INDEX idx_part_6_product_name_trgm ON food_nutrients_part_6 USING GIN (product_name gin_trgm_ops);
              CREATE INDEX idx_part_6_category_tags ON food_nutrients_part_6 USING GIN (category_tags);
              CREATE INDEX idx_part_6_brand_tag ON food_nutrients_part_6 (brand_tag);
              CREATE INDEX idx_part_6_code ON food_nutrients_part_6 (code);

              CREATE INDEX idx_part_7_search_vector ON food_nutrients_part_7 USING GIN (search_vector);
              CREATE INDEX idx_part_7_product_name_trgm ON food_nutrients_part_7 USING GIN (product_name gin_trgm_ops);
              CREATE INDEX idx_part_7_category_tags ON food_nutrients_part_7 USING GIN (category_tags);
              CREATE INDEX idx_part_7_brand_tag ON food_nutrients_part_7 (brand_tag);
              CREATE INDEX idx_part_7_code ON food_nutrients_part_7 (code);

        - sql:
            sql: |
              -- Create unique constraint on code across all partitions
              ALTER TABLE food_nutrients_partitioned ADD CONSTRAINT unique_code_partitioned UNIQUE (code);

      rollback:
        - sql:
            sql: |
              DROP TABLE IF EXISTS food_nutrients_partitioned CASCADE;
