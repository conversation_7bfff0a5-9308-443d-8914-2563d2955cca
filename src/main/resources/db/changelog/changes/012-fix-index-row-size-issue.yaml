databaseChangeLog:
  - changeSet:
      id: 012-fix-index-row-size-issue
      author: f<PERSON><PERSON><PERSON><PERSON>
      comment: "Create optimized indexes that avoid PostgreSQL row size limits"
      changes:
        - sql:
            sql: |
              -- Create optimized indexes for food search performance
              -- These indexes are designed to avoid the 2704 byte row size limit
              
              -- Index for exact product name matches (most common search pattern)
              CREATE INDEX idx_food_exact_name_match 
              ON food_nutrients (lower(product_name)) 
              WHERE product_name IS NOT NULL;

        - sql:
            sql: |
              -- Index for brand-based searches
              CREATE INDEX idx_food_brand_search 
              ON food_nutrients (brand_tag, code) 
              WHERE brand_tag IS NOT NULL;

        - sql:
            sql: |
              -- Index for nutrition-complete foods (for health-conscious searches)
              CREATE INDEX idx_food_nutrition_complete 
              ON food_nutrients (energy_kcal_100g, proteins_100g) 
              WHERE energy_kcal_100g IS NOT NULL 
                AND proteins_100g IS NOT NULL 
                AND carbohydrates_100g IS NOT NULL 
                AND fat_100g IS NOT NULL;

        - sql:
            sql: |
              -- Index for category-based filtering
              CREATE INDEX idx_food_category_filter 
              ON food_nutrients USING GIN (category_tags) 
              WHERE category_tags IS NOT NULL;

        - sql:
            sql: |
              -- Composite index for code and basic nutrition (small columns only)
              CREATE INDEX idx_food_code_nutrition 
              ON food_nutrients (code, energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g);

        - sql:
            sql: |
              -- Index for product name length-based optimization (for autocomplete)
              CREATE INDEX idx_food_name_length 
              ON food_nutrients (length(product_name), product_name) 
              WHERE product_name IS NOT NULL AND length(product_name) <= 50;

        - sql:
            sql: |
              -- Update table statistics for better query planning
              ANALYZE food_nutrients;

      rollback:
        - sql:
            sql: |
              DROP INDEX IF EXISTS idx_food_exact_name_match;
              DROP INDEX IF EXISTS idx_food_brand_search;
              DROP INDEX IF EXISTS idx_food_nutrition_complete;
              DROP INDEX IF EXISTS idx_food_category_filter;
              DROP INDEX IF EXISTS idx_food_code_nutrition;
              DROP INDEX IF EXISTS idx_food_name_length;
