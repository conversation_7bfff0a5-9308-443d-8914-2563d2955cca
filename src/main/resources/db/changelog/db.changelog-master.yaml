databaseChangeLog:
  - include:
      file: db/changelog/changes/001-initial-schema.yaml
  - include:
      file: db/changelog/changes/002-add-activity-level.yaml
  - include:
      file: db/changelog/changes/003-replace-age-with-dob.yaml
  - include:
      file: db/changelog/changes/004-add-guest-user-api-usage.yaml
  - include:
      file: db/changelog/changes/005-add-count-to-workout.yaml
  - include:
      file: db/changelog/changes/006-create-pg-trgm-extension.yaml
  - include:
      file: db/changelog/changes/007-create-brands-categories-countries.yaml
  - include:
      file: db/changelog/changes/008-create-food-nutrients.yaml
  - include:
      file: db/changelog/changes/009-create-food-indexes.yaml
  - include:
      file: db/changelog/changes/010-create-food-partitioning.yaml
  - include:
      file: db/changelog/changes/011-create-performance-indexes.yaml